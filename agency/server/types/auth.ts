import LinkType from "storeseo-enums/agency/linkTypes";
import TokenType from "storeseo-enums/agency/tokenTypes";
import AgencyUserStatus from "storeseo-enums/agency/userStatus";
type AgencyUserStatusType = (typeof AgencyUserStatus)[keyof typeof AgencyUserStatus];
type LinkTypeType = (typeof LinkType)[keyof typeof LinkType];

/**
 * User registration request data
 */
export interface IRegisterUserPayload {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

export interface IInviteUserPayload {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  workspaceId: number;
  role: string;
}

/**
 * User registration response data
 */
export interface IRegisterUserResponse {
  user: IUser;
  verificationToken: string;
}

/**
 * User data
 */
export interface IUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  status: AgencyUserStatusType;
  created_at: string;
  updated_at: string;
}

/**
 * User login request data
 */
export interface ILoginUserPayload {
  email: string;
  password: string;
}

export interface ILoginUserResponse {
  user: Omit<IUser, "password">;
  tokens: TokenPair;
}

export interface IWorkspaceSetupPayload {
  workspaceName: string;
  email: string;
  slug: string;
}

/**
 * Workspace setup response data
 */
export interface IWorkspaceSetupResult {
  user: {
    id: number;
    email: string;
    role: string;
    status: AgencyUserStatusType;
  };
  workspace: {
    id: number;
    name: string;
    slug: string;
  };
  // tokens: TokenPair;
}

/**
 * JWT token pair (access + refresh)
 */
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

/**
 * Verification response data
 */
export interface IVerifyUserReponse {
  id: number;
  email: string;
  verificationToken?: string;
}

/**
 * Verification token payload
 */
export interface VerificationTokenPayload {
  sub: string; // User ID
  email: string; // User email
  link_type: LinkTypeType;
  token_type: TokenType;
}

export interface IInvitationTokenPayload {
  email: string;
  link_type: LinkTypeType;
  token_type: TokenType;
  role: string;
  inviter_id: number;
  workspace_id: number;
}

/**
 * Access token payload
 */
export interface AccessTokenPayload {
  sub: string; // User ID
  email: string; // User email
  name: string; // Full name
  role: string; // User role
  scopes: string[]; // Permission scopes
  type: "access";
  iat: number; // Issued at
  exp: number; // Expires at (5 minutes)
}

/**
 * Refresh token payload
 */
export interface RefreshTokenPayload {
  sub: string; // User ID
  type: "refresh";
  iat: number; // Issued at
  exp: number; // Expires at (7 days)
}

/**
 * Database workspace model interface
 */
export interface IAgencyWorkspace {
  id: number;
  name: string;
  slug: string;
  settings: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

/**
 * Database user roles mapper model interface
 */
export interface IAgencyUserRolesMapper {
  id: number;
  user_id: string;
  workspace_id: string;
  role: string;
  created_at: Date;
  updated_at: Date;
}
