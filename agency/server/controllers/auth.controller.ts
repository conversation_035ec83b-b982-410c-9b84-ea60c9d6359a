import { AuthenticatedRequest, InvitationRequest } from "@/middlewares/auth.js";
import { IRegisterUserPayload, IUser } from "@/types/auth.js";
import config from "agency/server/config/config.js";
import type { Request, Response } from "express";
import httpStatus from "http-status";
import AppTypes from "storeseo-enums/appTypes";
import EventTopics from "storeseo-enums/eventTopics";
import queueConfig from "../../../web/api/queue/config.js";
import queueDispatcher from "../../../web/api/queue/queueDispatcher.js";
import AuthService from "../services/auth.service.js";
import UserService from "../services/user.service.js";
import ApiError from "../utils/apiError.js";
import catchAsync from "../utils/catchAsync.js";
const { QUEUE_NAMES } = queueConfig;
const { dispatchQueue } = queueDispatcher;

class AuthController {
  /**
   * Register a new user
   * POST /v1/auth/register
   */
  register = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const userData: IRegisterUserPayload = req.body;
    const user = await AuthService.registerUser(userData);

    //  Send verification email
    dispatchQueue({
      queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
      message: {
        topic: EventTopics.AGENCY_USER_VERIFICATION,
        deliveryDate: new Date(),
        emailOrigin: AppTypes.AGENCY,
        meta: {
          email: user.user.email,
          verificationToken: user.verificationToken,
        },
      },
    });

    res.status(httpStatus.CREATED).json({
      success: true,
      message: "Registration successful",
      data: user.user,
    });
  });

  /**
   * Verify email with token and set auth cookies
   * POST /v1/auth/verify-email
   */
  verifyUser = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const { secret } = req.query;
    const result = await AuthService.verifyUser(secret as string);

    res.status(httpStatus.OK).json({
      success: true,
      message: "Email verified successfully",
      data: result,
    });
  });

  /**
   * Resend verification email
   * POST /v1/auth/resend-verification-email
   */
  resendVerificationEmail = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const { email } = req.body;

    const result = await AuthService.resendVerificationEmail(email);

    // Send verification email
    dispatchQueue({
      queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
      message: {
        topic: EventTopics.AGENCY_USER_VERIFICATION,
        deliveryDate: new Date(),
        emailOrigin: AppTypes.AGENCY,
        meta: {
          email: result.email,
          verificationToken: result.verificationToken,
        },
      },
    });

    res.status(httpStatus.OK).json({
      success: true,
      message: "Verification email sent successfully",
      data: {},
    });
  });

  /**
   * Setup workspace for verified user
   * POST /v1/auth/create-workspace
   */
  createWorkspace = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const user = req.user;

    const result = await AuthService.createWorkspace(req.body, user as IUser);

    res.status(httpStatus.CREATED).json({
      success: true,
      message: "Workspace created successfully",
      data: result,
    });

    // res
    //   .cookie("accessToken", result.tokens.accessToken, {
    //     httpOnly: true,
    //     secure: process.env.NODE_ENV === "production",
    //     sameSite: "strict",
    //     maxAge: config.jwt.accessTokenExpirationDuration * 1000,
    //   })
    //   .cookie("refreshToken", result.tokens.refreshToken, {
    //     httpOnly: true,
    //     secure: process.env.NODE_ENV === "production",
    //     sameSite: "strict",
    //     maxAge: config.jwt.refreshTokenExpirationDuration * 1000,
    //   })
    //   .status(httpStatus.CREATED)
    //   .json({
    //     success: true,
    //     message: "Workspace created successfully",
    //     data: result,
    //   });
  });

  /**
   * Get current user profile with complete data
   * GET /v1/auth/me
   */
  getProfile = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const user = (req as AuthenticatedRequest).user;
    const profileData = await UserService.getUserProfile(user.id);

    res.status(httpStatus.OK).json({
      success: true,
      message: "User profile retrieved successfully",
      data: profileData,
    });
  });

  /**
   * Login user with email and password
   * POST /v1/auth/login
   */
  loginWithEmailAndPassword = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const result = await AuthService.loginWithEmailAndPassword(req.body);

    // Set httpOnly cookies
    res
      .cookie("accessToken", result.tokens.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: config.jwt.accessTokenExpirationDuration * 1000,
      })
      .cookie("refreshToken", result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: config.jwt.refreshTokenExpirationDuration * 1000,
      })
      .status(httpStatus.OK)
      .json({
        success: true,
        message: "Login successful",
        data: result,
      });
  });

  /**
   * Logout user - clears httpOnly cookies
   * POST /v1/auth/logout
   */
  logout = catchAsync(async (_req: Request, res: Response): Promise<void> => {
    // Clear httpOnly cookies
    res.clearCookie("accessToken").clearCookie("refreshToken").status(httpStatus.OK).json({
      success: true,
      message: "Logged out successfully",
      data: {},
    });
  });

  /**
   * Refresh access token
   * POST /v1/auth/refresh-token
   */
  refreshToken = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const cookieToken = req?.cookies?.refreshToken;
    const refreshToken = cookieToken;

    if (!refreshToken) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Refresh token is required");
    }

    const tokens = await AuthService.refreshTokens(refreshToken);

    // Optionally set new cookies so subsequent requests use refreshed tokens
    res
      .cookie("accessToken", tokens.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: config.jwt.accessTokenExpirationDuration * 1000,
      })
      .cookie("refreshToken", tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: config.jwt.refreshTokenExpirationDuration * 1000,
      })
      .status(httpStatus.OK)
      .json({
        success: true,
        message: "Tokens refreshed successfully",
        data: { tokens },
      });
  });

  /**
   * Invite user
   * POST /v1/auth/invite-user
   */
  inviteUser = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const { email, role } = req.body;
    const user = (req as AuthenticatedRequest).user;

    const result = await AuthService.inviteUser(email, user.id, role);

    // Send invitation email (with invitation link)
    dispatchQueue({
      queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
      message: {
        topic: EventTopics.AGENCY_USER_INVITE_VERIFICATION,
        deliveryDate: new Date(),
        emailOrigin: AppTypes.AGENCY,
        meta: {
          email,
          verificationToken: result.invitationToken,
        },
      },
    });

    res.status(httpStatus.OK).json({
      success: true,
      message: "User invited successfully",
      data: result.user,
    });
  });

  /**
   * Verify invitation
   * GET /v1/auth/verify-invitation
   */
  verifyInvitation = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const invitation = (req as InvitationRequest).invitation;
    const result = await AuthService.verifyInvitation(invitation);
    res.status(httpStatus.OK).json({
      success: true,
      message: "Invitation verified successfully",
      data: result,
    });
  });

  /**
   * Invite user registration
   * POST /v1/auth/invite-user-register
   */
  inviteUserRegister = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const invitation = (req as InvitationRequest).invitation;
    const userData = req.body;
    const result = await AuthService.inviteUserRegister(userData, invitation);

    res
      .cookie("accessToken", result.tokens.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: config.jwt.accessTokenExpirationDuration * 1000,
      })
      .cookie("refreshToken", result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: config.jwt.refreshTokenExpirationDuration * 1000,
      })
      .status(httpStatus.OK)
      .json({
        success: true,
        message: "User registered successfully",
        data: result,
      });
  });

  /**
   * Join workspace
   * POST /v1/auth/join-workspace
   */
  joinWorkspace = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const { email } = req.body;
    const invitation = (req as InvitationRequest).invitation;

    const result = await AuthService.joinWorkspace(email, invitation);

    res
      .cookie("accessToken", result.tokens.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: config.jwt.accessTokenExpirationDuration * 1000,
      })
      .cookie("refreshToken", result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: config.jwt.refreshTokenExpirationDuration * 1000,
      })
      .status(httpStatus.OK)
      .json({
        success: true,
        message: "Successfully joined workspace",
        data: result,
      });
  });
}

export default new AuthController();
