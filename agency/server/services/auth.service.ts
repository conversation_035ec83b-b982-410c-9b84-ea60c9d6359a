import {
  IInvitationTokenPayload,
  IInviteUserPayload,
  ILoginUserPayload,
  ILoginUserResponse,
  IRegisterUserPayload,
  IRegisterUserResponse,
  IUser,
  IVerifyUserReponse,
  IWorkspaceSetupPayload,
  IWorkspaceSetupResult,
} from "agency/server/types/auth.js";
import exclude from "agency/server/utils/exclude.js";
import bcrypt from "bcrypt";
import httpStatus from "http-status";
import jwt from "jsonwebtoken";
import AgencyUserRole from "storeseo-enums/agency/userRoles";
import AgencyUserStatus from "storeseo-enums/agency/userStatus";
import LinkType from "web/packages/storeseo-enums/dist/agency/linkTypes.js";
import sequelizeModels from "../../../web/sequelize/index.js";
import ApiError from "../utils/apiError.js";
import TokenService from "./token.service.js";
import UserService from "./user.service.js";
import WorkspaceService from "./workspace.service.js";
const { AgencyUser, AgencyUserRolesMapper, AgencyWorkspace } = sequelizeModels;

class AuthService {
  /**
   * Register a new user
   */
  async registerUser(userData: IRegisterUserPayload): Promise<IRegisterUserResponse> {
    try {
      const { firstName, lastName, email, password } = userData;
      const existingUser = await UserService.getUserByEmail(email);

      if (existingUser) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Email already exists");
      }

      // Hash password
      const saltRounds = Number(process.env.HASH_SALT_ROUNDS);
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Create user
      const createdUser = await AgencyUser.create({
        firstName,
        lastName,
        email,
        password: hashedPassword,
        status: AgencyUserStatus.VERIFICATION_PENDING,
      });

      const user: IUser = createdUser?.toJSON();

      // Generate verification token
      const verificationToken = TokenService.generateVerificationToken({
        id: user.id,
        email: user.email,
      });

      const userResponse = {
        user: exclude(user, ["password"]) as IUser,
        verificationToken,
      };

      return userResponse;
    } catch (error: unknown) {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * Verify user with token and generate auth tokens
   */
  async verifyUser(token: string): Promise<any> {
    try {
      // token verification
      const decodedToken = await TokenService.verifyToken(token);

      // Check if token is verification token
      if (decodedToken.link_type !== LinkType.VERIFICATION) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Invalid Token");
      }

      // Get user
      const registeredUser = await UserService.getUserByEmail(decodedToken.email);

      if (!registeredUser) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found");
      }

      if (registeredUser.status === AgencyUserStatus.VERIFIED) {
        throw new ApiError(httpStatus.BAD_REQUEST, "User already verified");
      }

      const { 1: updatedUser } = await AgencyUser.update(
        {
          status: AgencyUserStatus.VERIFIED,
        },
        { where: { id: registeredUser.id }, returning: true }
      );

      const user: IUser = updatedUser[0]?.toJSON();

      // Generate auth tokens for the verified user
      // const tokens = await TokenService.generateAuthTokens(user);

      const response = {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          status: user.status,
          created_at: user.created_at,
          updated_at: user.updated_at,
        },
        // tokens,
      };

      return response;
    } catch (error: unknown) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid or expired verification token");
      }
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * Resend verification email
   */
  async resendVerificationEmail(email: string): Promise<IVerifyUserReponse> {
    try {
      const user: IUser = await UserService.getUserByEmail(email);

      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found");
      }

      if (user.status === AgencyUserStatus.VERIFIED) {
        throw new ApiError(httpStatus.BAD_REQUEST, "User already verified");
      }

      // Generate verification token
      const verificationToken = TokenService.generateVerificationToken({
        id: user.id,
        email: user.email,
      });

      return {
        id: user.id,
        email: user.email,
        verificationToken,
      };
    } catch (error: unknown) {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * Setup workspace for verified user
   */
  async createWorkspace(payload: IWorkspaceSetupPayload, user: IUser): Promise<IWorkspaceSetupResult> {
    try {
      const { workspaceName, slug } = payload;

      const verifiedUser: IUser | null = await UserService.getUserByEmail(user.email);
      if (!verifiedUser) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found");
      }

      if (verifiedUser.status !== AgencyUserStatus.VERIFIED) {
        throw new ApiError(httpStatus.BAD_REQUEST, "User not verified");
      }

      const workspace = await WorkspaceService.createWorkspace(workspaceName, slug, verifiedUser.id);

      // Create user role mapping
      const result = await AgencyUserRolesMapper.create({
        user_id: verifiedUser.id,
        workspace_id: workspace.id,
        role: AgencyUserRole.OWNER,
      });

      const userRole = result?.toJSON();

      // Generate tokens with role and workspace info
      // const tokens = await TokenService.generateAuthTokens(verifiedUser);

      return {
        user: {
          id: verifiedUser.id,
          email: verifiedUser.email,
          role: userRole.role,
          status: verifiedUser.status,
        },
        workspace: {
          id: workspace.id,
          name: workspace.name,
          slug: workspace.slug,
        },
        // tokens,
      };
    } catch (error: unknown) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid or expired verification token");
      }
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * Login user with email and password
   */
  async loginWithEmailAndPassword(loginData: ILoginUserPayload): Promise<ILoginUserResponse> {
    try {
      const { email, password } = loginData;

      const user: IUser | null = await UserService.getUserByEmail(email);

      if (!user) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Invalid user");
      }

      // Check if user is verified
      if (user.status !== AgencyUserStatus.VERIFIED) {
        throw new ApiError(httpStatus.UNAUTHORIZED, "Please verify your email before logging in");
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Invalid email or password");
      }

      const authTokens = await TokenService.generateAuthTokens(user);

      return {
        user: {
          ...(exclude(user, ["password"]) as IUser),
        },
        tokens: authTokens,
      };
    } catch (error: unknown) {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * Refresh access token
   */
  async refreshTokens(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Verify refresh token
      // const decoded = jwt.verify(refreshToken, config.jwt.secret) as RefreshTokenPayload;
      const decoded = await TokenService.verifyToken(refreshToken);

      // Find user
      const user: IUser | null = await UserService.getUserById(Number(decoded.sub));

      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found");
      }

      const tokens = await TokenService.generateAuthTokens(user);
      return tokens;
    } catch (error: unknown) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error("Invalid or expired refresh token");
      }
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * Invite user
   */
  async inviteUser(email: string, inviterId: number, role: AgencyUserRole): Promise<Record<string, any>> {
    try {
      const inviter = await UserService.getUserById(inviterId);
      if (!inviter) {
        throw new ApiError(httpStatus.NOT_FOUND, "Inviter not found");
      }

      // find inviter's OWNER workspace mapping
      const ownerMapping = await AgencyUserRolesMapper.findOne({
        where: { user_id: inviterId, role: AgencyUserRole.OWNER },
      });

      if (!ownerMapping) {
        throw new ApiError(httpStatus.FORBIDDEN, "Only workspace owners can invite users");
      }
      const workspace = ownerMapping?.toJSON();

      const invitationToken = TokenService.generateInvitationToken({
        email,
        role,
        inviter_id: inviterId,
        workspace_id: workspace.workspace_id,
      });

      const response = {
        user: {
          email,
          workspace_id: workspace.workspace_id,
        },
        invitationToken,
      };

      return response;
    } catch (error: unknown) {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * Verify invitation token and return email, role, workspace
   * Token is already verified by invitationAuth middleware
   */
  async verifyInvitation(decoded: IInvitationTokenPayload) {
    try {
      const existingUser: IUser | null = await UserService.getUserByEmail(decoded.email);

      // get the invited workspace details
      const ownerMapping = await AgencyUserRolesMapper.findOne({
        where: { user_id: decoded.inviter_id, role: AgencyUserRole.OWNER },
        include: [
          {
            model: AgencyWorkspace,
            as: "workspace",
          },
        ],
      });

      if (!ownerMapping) {
        throw new ApiError(httpStatus.NOT_FOUND, "Workspace not found or inviter is not an owner");
      }

      const userMapping = ownerMapping?.toJSON();

      // Check if user is already a member of this workspace
      if (existingUser) {
        const existingMembership = await AgencyUserRolesMapper.findOne({
          where: {
            user_id: existingUser.id,
            workspace_id: userMapping.workspace_id,
          },
        });

        if (existingMembership) {
          throw new ApiError(httpStatus.BAD_REQUEST, "User is already a member of this workspace");
        }
      }

      return {
        email: decoded.email,
        role: decoded.role,
        workspace: {
          id: userMapping.workspace_id,
          name: userMapping.workspace.name,
          slug: userMapping.workspace.slug,
        },
        userExists: !!existingUser,
      };
    } catch (error: unknown) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * Invite user registration - for new users who don't exist in the system
   * Token is already verified by invitationAuth middleware
   */
  async inviteUserRegister(
    userData: IInviteUserPayload,
    decoded: IInvitationTokenPayload
  ): Promise<ILoginUserResponse> {
    try {
      // 1) Validate that the email matches the invitation
      // if (decoded.email !== userData.email) {
      //   throw new ApiError(httpStatus.BAD_REQUEST, "Email does not match invitation");
      // }

      // 2) Check if user already exists (should not for this flow)
      const existingUser = await UserService.getUserByEmail(userData.email);
      if (existingUser) {
        throw new ApiError(httpStatus.BAD_REQUEST, "User already exists");
      }

      // 3) Verify workspace and inviter
      const ownerMapping = await AgencyUserRolesMapper.findOne({
        where: { user_id: decoded.inviter_id, role: AgencyUserRole.OWNER },
        include: [
          {
            model: AgencyWorkspace,
            as: "workspace",
          },
        ],
      });

      if (!ownerMapping) {
        throw new ApiError(httpStatus.NOT_FOUND, "Workspace not found or inviter is not an owner");
      }

      const { firstName, lastName, password, email } = userData;

      // 5) Create new user
      const saltRounds = Number(process.env.HASH_SALT_ROUNDS);
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      const createdUser = await AgencyUser.create({
        firstName,
        lastName,
        email,
        password: hashedPassword,
        status: AgencyUserStatus.VERIFIED,
      });

      const user: IUser = createdUser.toJSON();

      // 6) Create user role mapping
      await AgencyUserRolesMapper.create({
        user_id: user.id,
        workspace_id: decoded.workspace_id,
        role: decoded.role,
      });

      // 7) Generate tokens
      const tokens = await TokenService.generateAuthTokens(user);

      return {
        user: exclude(user, ["password"]) as IUser,
        tokens,
      };
    } catch (error: unknown) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid or expired invitation token");
      }
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * Join workspace - for existing users who received an invitation
   */
  async joinWorkspace(email: string, decoded: IInvitationTokenPayload): Promise<any> {
    try {
      // 1) Validate that the email matches the invitation
      if (decoded.email !== email) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Email does not match invitation");
      }

      // 2) Check if user exists
      const existingUser = await UserService.getUserByEmail(email);
      if (!existingUser) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found. Please complete registration first.");
      }

      // 3) Verify workspace and inviter
      const ownerMapping = await AgencyUserRolesMapper.findOne({
        where: { user_id: decoded.inviter_id, role: AgencyUserRole.OWNER },
        include: [
          {
            model: AgencyWorkspace,
            as: "workspace",
          },
        ],
      });

      if (!ownerMapping) {
        throw new ApiError(httpStatus.NOT_FOUND, "Workspace not found or inviter is not an owner");
      }

      // 5) Check if user is already a member of this workspace
      const existingMembership = await AgencyUserRolesMapper.findOne({
        where: {
          user_id: existingUser.id,
          workspace_id: decoded.workspace_id,
        },
      });

      if (existingMembership) {
        throw new ApiError(httpStatus.BAD_REQUEST, "User is already a member of this workspace");
      }

      // 6) Create user role mapping
      const result = await AgencyUserRolesMapper.create({
        user_id: existingUser.id,
        workspace_id: decoded.workspace_id,
        role: decoded.role,
      });

      const tokens = await TokenService.generateAuthTokens(existingUser);

      return {
        // userRoleMapping: result?.toJSON(),
        // workspace: ownerMapping?.toJSON().workspace,
        user: exclude(existingUser, ["password"]) as IUser,
        tokens,
      };
    } catch (error: unknown) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid or expired invitation token");
      }
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }
}

export default new AuthService();
