import httpStatus from "http-status";
import sequelizeModels from "../../../web/sequelize/index.js";
import { rolesScopeMap } from "../config/userRoleScopes.js";
import ApiError from "../utils/apiError.js";
import exclude from "../utils/exclude.js";
const { AgencyUser, AgencyUserRolesMapper, AgencyWorkspace } = sequelizeModels;

class UserService {
  /**
   * Get all users
   */
  async getUsers() {
    const users = await AgencyUser.findAll();
    return users.map((u) => u.toJSON());
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: number) {
    const user = await AgencyUser.findByPk(userId);
    return user?.toJSON();
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string) {
    const user = await AgencyUser.findOne({
      where: { email },
    });
    return user?.toJSON();
  }

  /**
   * Get user by conditions
   */
  async getUserByConditions(conditions: Record<string, any>) {
    const user = await AgencyUser.findOne({
      where: conditions,
    });
    return user?.toJSON();
  }

  /**
   * Get complete user profile with workspace and role information
   */
  async getUserProfile(userId: number) {
    try {
      // Get user data
      const user = await this.getUserById(userId);

      console.log("user", user);
      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found");
      }

      // Get user role and workspace info
      const getUserRoleMapper = await AgencyUserRolesMapper.findOne({
        where: { user_id: userId.toString() },
        include: [
          {
            model: AgencyWorkspace,
            as: "workspace",
          },
        ],
      });

      if (!getUserRoleMapper) {
        return {
          user: {
            ...user,
            // scopes: rolesScopeMap[AgencyUserRole.MEMBER as keyof typeof rolesScopeMap]?.actions || [],
          },
          workspace: null,
        };
      }

      // if (!getUserRoleMapper) {
      //   throw new ApiError(httpStatus.BAD_REQUEST, "User workspace not found");
      // }

      const userRole = getUserRoleMapper?.toJSON();

      const userData = exclude(user, ["password"]);
      const userScopes = rolesScopeMap[userRole.role as keyof typeof rolesScopeMap]?.actions || [];

      return {
        user: {
          ...userData,
          role: userRole.role,
          scopes: userScopes,
        },
        workspace: userRole.workspace,
      };
    } catch (error) {
      console.log("error", error);
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error instanceof Error ? error.message : "Unknown error");
    }
  }
}

export default new UserService();
