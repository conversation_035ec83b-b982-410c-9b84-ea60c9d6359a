import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, InlineStack, Text, TextField } from "@storeware/polaris";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router";
import type { ApiResponse } from "~/frontend/types/auth";
import type { Route } from "../../.react-router/types/app/routes/+types/verify-invitation";
import { useJoinWorkspace, useVerifyInvitation } from "../../api/invitations";
import { useInvitation } from "../../contexts/InvitationContext";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "StoreSEO Agency - Invitation Verification" },
    { name: "description", content: "Verify your invitation" },
  ];
}

export default function InvitationVerify() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [state, setState] = useState<{ status: "loading" | "success" | "error" | "form"; message: string }>({
    status: "loading",
    message: "Verifying your invitation...",
  });

  const { setInvitationSecret, clearInvitationSecret } = useInvitation();

  const [invitationData, setInvitationData] = useState<{
    email: string;
    role: string;
    workspace: { id: number; name: string; slug: string; role: string };
  } | null>(null);

  const { mutate: handleVerifyInvitation } = useVerifyInvitation();
  const { mutate: handleJoinWorkspace } = useJoinWorkspace();

  const handleAcceptInvitation = () => {
    const token = searchParams.get("secret");
    if (!token || !invitationData?.email) {
      setState({ status: "error", message: "Missing invitation data" });
      return;
    }

    handleJoinWorkspace(
      {
        email: invitationData.email,
        secret: token,
      },
      {
        onSuccess: async () => {
          clearInvitationSecret();
          navigate("/dashboard");
        },
      }
    );
  };

  const handleCancelInvitation = () => {
    navigate("/dashboard");
  };

  const verify = async () => {
    const token = searchParams.get("secret");
    if (!token) {
      setState({ status: "error", message: "Missing invitation token" });
      return;
    }

    handleVerifyInvitation(token, {
      onSuccess: (response: ApiResponse<any>) => {
        const { email, role, workspace, userExists } = response.data;

        setState({ status: "success", message: "Invitation verified!" });

        // Store invitation data for modal or navigation
        const invitationInfo = {
          email,
          role,
          workspace: {
            id: workspace.id,
            name: workspace.name,
            slug: workspace.slug,
            role: role,
          },
          secret: token,
        };

        // Store in context for persistence across page refreshes
        setInvitationSecret({ secret: token, email });
        setInvitationData(invitationInfo);

        if (userExists) {
          // Show form for existing users
          setTimeout(() => {
            setState({ status: "form", message: "Join Workspace" });
          }, 1000);
        } else {
          // Redirect to registration for new users
          setTimeout(() => {
            navigate("/invitation-registration");
          }, 1000);
        }
      },
      onError: (err: any) => setState({ status: "error", message: err.response.data.message }),
    });
  };

  useEffect(() => {
    verify();
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card>
          <Box padding="600">
            {state.status === "form" ? (
              <BlockStack gap="400">
                <Text
                  as="h1"
                  variant="headingLg"
                  alignment="center"
                >
                  {state.message}
                </Text>

                <BlockStack gap="300">
                  <TextField
                    label="Workspace"
                    value={invitationData?.workspace.slug || ""}
                    disabled
                    autoComplete="off"
                  />

                  <TextField
                    label="Role"
                    value={invitationData?.role || ""}
                    disabled
                    autoComplete="off"
                  />

                  <Text
                    as="p"
                    variant="bodyMd"
                    alignment="center"
                  >
                    You have been invited to join <strong>{invitationData?.workspace.name}</strong> as a{" "}
                    <strong>{invitationData?.role}</strong>.
                  </Text>
                </BlockStack>

                <InlineStack
                  gap="300"
                  align="center"
                >
                  <Button
                    variant="tertiary"
                    onClick={handleCancelInvitation}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleAcceptInvitation}
                  >
                    Join Workspace
                  </Button>
                </InlineStack>
              </BlockStack>
            ) : (
              <BlockStack
                gap="400"
                align="center"
              >
                <Text
                  as="h1"
                  variant="headingLg"
                  alignment="center"
                >
                  {state.status === "loading" ? "Verifying invitation" : state.message}
                </Text>
                {state.status === "error" && <Button onClick={() => navigate("/")}>Go Home</Button>}
              </BlockStack>
            )}
          </Box>
        </Card>
      </div>
    </div>
  );
}
