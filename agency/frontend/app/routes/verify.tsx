import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Text } from "@storeware/polaris";
import { useEffect, useState } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router";
import type { Route } from "../../.react-router/types/app/routes/+types/verify";
import { useVerifyEmail } from "../../api/auth";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "StoreSEO Agency - Email Verification" },
    { name: "description", content: "Verify your email address" },
  ];
}

interface VerificationState {
  status: "loading" | "success" | "error" | "instructions";
  message: string;
  email?: string;
}

export default function Verify() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const [state, setState] = useState<VerificationState>({
    status: "loading",
    message: "Verifying your email...",
  });

  const { mutate: handleVerifyEmail } = useVerifyEmail();

  const verifyEmail = async () => {
    const token = searchParams.get("secret");

    if (!token) {
      // No token, show instructions or get email from navigation state
      const email = location.state?.email;
      const message = location.state?.message || "Please check your email for the verification link.";

      setState({
        status: "instructions",
        message,
        email,
      });
      return;
    }

    try {
      handleVerifyEmail(token, {
        onSuccess: () => {
          setState({
            status: "success",
            message: "Email verified successfully!",
          });

          // Redirect to login page after a short delay
          // User is already authenticated via cookies set by the backend
          setTimeout(() => {
            navigate("/login");
          }, 2000);
        },
      });
    } catch (error) {
      console.error("Email verification error:", error);
      setState({
        status: "error",
        message: "An unexpected error occurred. Please try again.",
      });
    }
  };

  useEffect(() => {
    verifyEmail();
  }, []);

  const handleResendEmail = async () => {
    // This would call a resend verification email endpoint
    // For now, just show a message
    setState((prev) => ({
      ...prev,
      message: "Verification email resent! Please check your inbox.",
    }));
  };

  const renderContent = () => {
    switch (state.status) {
      case "loading":
        return (
          <BlockStack
            gap="400"
            align="center"
          >
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center animate-pulse">
              <Text
                as="span"
                variant="headingLg"
              >
                ⏳
              </Text>
            </div>
            <Text
              as="h1"
              variant="headingLg"
              alignment="center"
            >
              Verifying Your Email
            </Text>
            <Text
              as="p"
              variant="bodyMd"
              tone="subdued"
              alignment="center"
            >
              Please wait while we verify your email address...
            </Text>
          </BlockStack>
        );

      case "success":
        return (
          <BlockStack
            gap="400"
            align="center"
          >
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <Text
                as="span"
                variant="headingLg"
                tone="success"
              >
                ✓
              </Text>
            </div>
            <Text
              as="h1"
              variant="headingLg"
              alignment="center"
            >
              Email Verified Successfully!
            </Text>
            <Text
              as="p"
              variant="bodyMd"
              tone="subdued"
              alignment="center"
            >
              {state.message}
            </Text>
            <Text
              as="p"
              variant="bodySm"
              tone="subdued"
              alignment="center"
            >
              Redirecting you to workspace setup...
            </Text>
          </BlockStack>
        );

      case "error":
        return (
          <BlockStack
            gap="400"
            align="center"
          >
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <Text
                as="span"
                variant="headingLg"
                tone="critical"
              >
                ✗
              </Text>
            </div>
            <Text
              as="h1"
              variant="headingLg"
              alignment="center"
            >
              Verification Failed
            </Text>
            <Text
              as="p"
              variant="bodyMd"
              tone="critical"
              alignment="center"
            >
              {state.message}
            </Text>
            <BlockStack gap="300">
              <Button
                variant="primary"
                onClick={() => navigate("/register")}
              >
                Back to Registration
              </Button>
              {state.email && (
                <Button
                  variant="secondary"
                  onClick={handleResendEmail}
                >
                  Resend Verification Email
                </Button>
              )}
            </BlockStack>
          </BlockStack>
        );

      case "instructions":
        return (
          <BlockStack
            gap="400"
            align="center"
          >
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <Text
                as="span"
                variant="headingLg"
              >
                📧
              </Text>
            </div>
            <Text
              as="h1"
              variant="headingLg"
              alignment="center"
            >
              Check Your Email
            </Text>
            <Text
              as="p"
              variant="bodyMd"
              alignment="center"
            >
              {state.message}
            </Text>
            {state.email && (
              <Text
                as="p"
                variant="bodySm"
                tone="subdued"
                alignment="center"
              >
                We sent a verification link to <strong>{state.email}</strong>
              </Text>
            )}
            <Text
              as="p"
              variant="bodySm"
              tone="subdued"
              alignment="center"
            >
              Click the link in the email to verify your account and continue.
            </Text>
            {/* <BlockStack gap="300">
              <Button
                variant="secondary"
                onClick={handleResendEmail}
              >
                Resend Verification Email
              </Button>
              <Button
                variant="plain"
                onClick={() => navigate("/register")}
              >
                Back to Registration
              </Button>
            </BlockStack> */}
          </BlockStack>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card>
          <Box padding="600">{renderContent()}</Box>
        </Card>
      </div>
    </div>
  );
}
