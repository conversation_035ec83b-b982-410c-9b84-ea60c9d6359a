import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Text, TextField } from "@storeware/polaris";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useLocation, useNavigate } from "react-router";
import type { Route } from "../../.react-router/types/app/routes/+types/setup-workspace";
import { useCreateWorkspace } from "../../api/auth";
import ProtectedRoute from "../../components/ProtectedRoute";
import { useAuth } from "../../contexts/AuthContext";

export function meta({}: Route.MetaArgs) {
  return [{ title: "StoreSEO Agency - Workspace Setup" }, { name: "description", content: "Set up your workspace" }];
}

export function SetupWorkspacePage() {
  const navigate = useNavigate();
  const location = useLocation();
  const { userProfile, logout, user } = useAuth();
  const { mutate: handleCreateWorkspace, isPending } = useCreateWorkspace();
  const [slugPreview, setSlugPreview] = useState("");

  console.log("user from workspace", user);

  interface IWorkspaceSetupFormData {
    workspaceName: string;
  }

  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
    clearErrors,
    watch,
  } = useForm<IWorkspaceSetupFormData>({
    defaultValues: {
      workspaceName: "",
    },
  });

  // Watch the name field for slug preview
  const watchedName = watch("workspaceName");

  // Generate slug preview when workspace name changes
  useEffect(() => {
    if (watchedName) {
      const slug = watchedName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
      setSlugPreview(slug);
    } else {
      setSlugPreview("");
    }
  }, [watchedName]);

  const onSubmit = async (data: IWorkspaceSetupFormData) => {
    console.log("data", data, location.state);
    try {
      clearErrors(); // Clear any previous errors

      handleCreateWorkspace(
        {
          workspaceName: data.workspaceName,
          // email: location?.state?.email,
          slug: slugPreview,
          // token,
        },
        {
          onSuccess: async () => {
            // await checkAuth();
            navigate("/dashboard");
          },
        }
      );
    } catch (error) {
      console.error("Workspace creation failed:", error);
      setError("root", { message: "An unexpected error occurred. Please try again." });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8">
        <Box>
          <Card>
            <form onSubmit={handleSubmit(onSubmit)}>
              <Text
                as="h1"
                variant="headingLg"
                alignment="center"
              >
                Set Up Your Workspace
              </Text>
              <Text
                as="p"
                variant="bodyMd"
                tone="subdued"
                alignment="center"
              >
                Create your workspace to get started with StoreSEO Agency
              </Text>

              {/* General Error Display */}
              {errors.root && (
                <Box paddingBlockStart="400">
                  <Text
                    as="p"
                    tone="critical"
                    variant="bodyMd"
                  >
                    {errors.root.message}
                  </Text>
                </Box>
              )}

              {/* Form Fields */}
              <Box
                paddingBlockStart="400"
                paddingBlockEnd="400"
              >
                <BlockStack gap="400">
                  <Controller
                    name="workspaceName"
                    control={control}
                    render={({ field, fieldState }) => (
                      <TextField
                        label="Workspace Name"
                        type="text"
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        error={fieldState.error?.message}
                        placeholder="Enter your workspace name"
                        disabled={isPending}
                        helpText="This will be the name of your agency workspace"
                        autoComplete="organization"
                      />
                    )}
                  />

                  {/* Slug Preview */}
                  {slugPreview && (
                    <Box>
                      <Text
                        as="p"
                        variant="bodySm"
                        tone="subdued"
                      >
                        Workspace URL: <strong>agency.storeseo.com/{slugPreview}</strong>
                      </Text>
                    </Box>
                  )}
                </BlockStack>
              </Box>

              {/* Submit Button */}
              <Button
                variant="primary"
                size="large"
                fullWidth
                submit
                loading={isPending}
                disabled={isPending}
              >
                {isPending ? "Creating Workspace..." : "Create Workspace"}
              </Button>

              {/* Back Link */}
              <Box paddingBlockStart="400">
                <Text
                  as="p"
                  variant="bodyMd"
                  alignment="center"
                >
                  <Button
                    variant="plain"
                    onClick={logout}
                    // onClick={() => navigate("/register")}
                    disabled={isPending}
                  >
                    Logout
                  </Button>
                </Text>
              </Box>
            </form>
          </Card>
        </Box>
      </div>
    </div>
  );
}

export default function SetupWorkspace() {
  return (
    <ProtectedRoute>
      <SetupWorkspacePage />
    </ProtectedRoute>
  );
}
