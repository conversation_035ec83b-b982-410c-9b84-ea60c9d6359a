import { yupResolver } from "@hookform/resolvers/yup";
import { BlockStack, Box, Button, Card, Text, TextField } from "@storeware/polaris";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useNavigate } from "react-router";
import * as yup from "yup";
import type { Route } from "../../.react-router/types/app/routes/+types/invitation-registration";
import { useInviteUserRegister } from "../../api/invitations";
import { useInvitation } from "../../contexts/InvitationContext";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "StoreSEO Agency - Invitation Registration" },
    { name: "description", content: "Complete your invited account setup" },
  ];
}

const schema = yup.object({
  firstName: yup.string().required().min(2).max(50),
  lastName: yup.string().optional(),
  password: yup.string().required().min(8).max(255),
});

export default function InvitationRegistration() {
  const navigate = useNavigate();
  const { invitationSecret, clearInvitationSecret, isLoading } = useInvitation();
  const { mutate: handleRegister, isPending } = useInviteUserRegister();

  // Redirect to dashboard if no invitation data is available
  useEffect(() => {
    if (!isLoading && !invitationSecret) {
      navigate("/dashboard");
    }
  }, [isLoading, invitationSecret]);

  // Don't render if still loading or no invitation data
  if (isLoading || !invitationSecret) {
    console.log("hitting loading block");
    return null;
  }

  const { email, secret } = invitationSecret;

  const { control, handleSubmit } = useForm<{ firstName: string; lastName?: string; password: string }>({
    resolver: yupResolver(schema),
    defaultValues: { firstName: "", lastName: "", password: "" },
  });

  const onSubmit = (data: { firstName: string; lastName?: string; password: string }) => {
    handleRegister(
      { ...data, email, secret },
      {
        onSuccess: async () => {
          clearInvitationSecret();
          navigate("/dashboard");
        },
      }
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card>
          <Box padding="600">
            <BlockStack gap="500">
              <Text
                as="h1"
                variant="headingLg"
              >
                Join Workspace
              </Text>
              {/* <Text
                as="p"
                variant="bodyMd"
                tone="subdued"
              >
                Workspace: <strong>{workspace?.name}</strong> ({workspace?.slug})
              </Text> */}

              <TextField
                autoComplete="off"
                label="Email"
                type="email"
                value={email || ""}
                disabled
              />

              <Controller
                name="firstName"
                control={control}
                render={({ field, fieldState }) => (
                  <TextField
                    autoComplete="off"
                    label="First name"
                    type="text"
                    value={field.value}
                    onChange={field.onChange}
                    error={fieldState.error?.message}
                    disabled={isPending}
                  />
                )}
              />

              <Controller
                name="lastName"
                control={control}
                render={({ field, fieldState }) => (
                  <TextField
                    autoComplete="off"
                    label="Last name"
                    type="text"
                    value={field.value}
                    onChange={field.onChange}
                    error={fieldState.error?.message}
                    disabled={isPending}
                  />
                )}
              />

              <Controller
                name="password"
                control={control}
                render={({ field, fieldState }) => (
                  <TextField
                    label="Password"
                    type="password"
                    value={field.value}
                    onChange={field.onChange}
                    error={fieldState.error?.message}
                    disabled={isPending}
                    autoComplete="new-password"
                  />
                )}
              />

              <Button
                variant="primary"
                onClick={handleSubmit(onSubmit)}
                loading={isPending}
                className="mt-4"
              >
                Complete Registration
              </Button>
            </BlockStack>
          </Box>
        </Card>
      </div>
    </div>
  );
}
