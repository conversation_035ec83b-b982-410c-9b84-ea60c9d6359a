import { MenuIcon } from "@shopify/polaris-icons";
import { Button, Icon, Text } from "@storeware/polaris";
import type { ReactNode } from "react";
import { useEffect, useState } from "react";
import { useAuth } from "../contexts/AuthContext";
import ProfileMenu from "./ProfileMenu";
import ProtectedRoute from "./ProtectedRoute";
import Sidebar from "./Sidebar";

interface DashboardLayoutProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
}

function DashboardLayoutContent({ children, title, subtitle }: DashboardLayoutProps) {
  const { user, workspace } = useAuth();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Close mobile sidebar when window is resized to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 768) {
        setIsMobileSidebarOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Prevent body scrolling when mobile sidebar is open
  useEffect(() => {
    if (isMobileSidebarOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isMobileSidebarOpen]);

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const closeMobileSidebar = () => {
    setIsMobileSidebarOpen(false);
  };

  return (
    <div className="dashboard-layout">
      <Sidebar
        isOpen={isMobileSidebarOpen}
        onClose={closeMobileSidebar}
      />

      {/* Mobile backdrop */}
      {isMobileSidebarOpen && (
        <div
          className="mobile-sidebar-backdrop"
          onClick={closeMobileSidebar}
        />
      )}

      <div className="dashboard-main">
        {/* Header */}
        <div className="dashboard-header">
          <div className="dashboard-header-content">
            <div className="dashboard-header-left">
              {/* Mobile hamburger menu */}
              <Button
                variant="tertiary"
                size="large"
                onClick={toggleMobileSidebar}
                className="mobile-menu-button"
                accessibilityLabel="Toggle navigation menu"
              >
                <Icon source={MenuIcon} />
              </Button>

              <div className="dashboard-header-info">
                <Text
                  as="h1"
                  variant="headingLg"
                >
                  {title || workspace?.name || "StoreSEO Agency"}
                </Text>
                {subtitle && (
                  <Text
                    as="p"
                    variant="bodySm"
                    tone="subdued"
                  >
                    {subtitle}
                  </Text>
                )}
                {!subtitle && (
                  <Text
                    as="p"
                    variant="bodySm"
                    tone="subdued"
                  >
                    Welcome back, {user?.firstName} {user?.lastName}
                  </Text>
                )}
              </div>
            </div>
            <ProfileMenu />
          </div>
        </div>

        {/* Main Content */}
        <div className="dashboard-content">
          <div className="dashboard-content-inner">{children}</div>
        </div>
      </div>
    </div>
  );
}

export default function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {
  return (
    <ProtectedRoute>
      <DashboardLayoutContent
        title={title}
        subtitle={subtitle}
      >
        {children}
      </DashboardLayoutContent>
    </ProtectedRoute>
  );
}
