export interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  status: string;
  scopes: string[];
}

export interface Workspace {
  id: number;
  name: string;
  slug: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export interface AuthState {
  user: User | null;
  workspace: Workspace | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface AuthContextType extends AuthState {
  login: (user: User, workspace: Workspace) => void; // convenience; internally triggers checkAuth
  logout: () => Promise<void>;
  // updateUser: (user: User) => void;
  // updateWorkspace: (workspace: Workspace) => void;
  userProfile: () => void;
  checkAuth: (force?: boolean) => Promise<void>;
}

export interface IInviteUserPayload {
  email: string;
  role: string;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface WorkspaceSetupData {
  name: string;
  email?: string;
  userId?: number;
}

export interface LoginResponse {
  user: User;
  workspace: Workspace;
}

export interface RegisterResponse {
  userId: number;
  email: string;
  verificationSent: boolean;
}

export interface VerifyEmailResponse {
  id: number;
  email: string;
  verificationToken: string;
}

export interface WorkspaceSetupResponse {
  user: User;
  workspace: Workspace;
}

export interface UserProfileResponse {
  user: User;
  workspace: Workspace;
}

/**
 * API response interface
 */
export interface ApiResponse<T> {
  data: T;
  message: string;
  status: number;
}
