import { createContext, useContext, useState, type ReactNode } from "react";
import { useNavigate } from "react-router";
import { useGetUserProfile, useLogout } from "../api/auth";
import type { ApiResponse, AuthContextType, AuthState, User, UserProfileResponse, Workspace } from "../types/auth";

const initialState: AuthState = {
  user: null,
  workspace: null,
  isAuthenticated: false,
  isLoading: false,
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [auth, setAuth] = useState<AuthState>(initialState);

  const navigate = useNavigate();

  const { mutate: handleLogout } = useLogout();
  const { mutate: handleGetUserProfile } = useGetUserProfile();

  /** Set auth state directly after login */
  // const login = (user: User, workspace: Workspace) => {
  //   setAuth({
  //     user,
  //     workspace,
  //     isAuthenticated: true,
  //     isLoading: false,
  //   });
  // };

  /** API logout + reset state */
  const logout = async () => {
    try {
      handleLogout(undefined);
      navigate("/login");
    } catch (error) {
      console.error("Logout API call failed:", error);
    } finally {
      setAuth(initialState);
    }
  };

  /** Update user/workspace separately */
  // const updateUser = (user: User) => setAuth((s) => ({ ...s, user }));

  // const updateWorkspace = (workspace: Workspace) => setAuth((s) => ({ ...s, workspace }));

  /** Fetch user/workspace from server */

  const userProfile = () => {
    setAuth((s) => ({ ...s, isLoading: true }));

    handleGetUserProfile(undefined, {
      onSuccess: (data: ApiResponse<UserProfileResponse>) => {
        const { user, workspace } = data.data;
        setAuth({ user, workspace, isAuthenticated: true, isLoading: false });
      },
      onError: () => {
        setAuth({ ...initialState, isLoading: false });
      },
    });
  };

  // const contextValue: AuthContextType = {
  //   ...auth,
  //   // login,
  //   logout,
  //   // updateUser,
  //   // updateWorkspace,
  //   // checkAuth,
  // };

  const contextValue = {
    ...auth,
    // login,
    logout,
    userProfile,

    // updateUser,
    // updateWorkspace,
    // checkAuth,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
