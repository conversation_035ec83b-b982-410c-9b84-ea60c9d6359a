import { createContext, useContext, useEffect, useState, type ReactNode } from "react";

// export interface InvitationData {
//   email: string;
//   role: string;
//   workspace: {
//     id: number;
//     name: string;
//     slug: string;
//     role: string;
//   };
//   secret: string;
// }

export interface InvitationSecret {
  secret: string;
  email: string;
}

interface InvitationState {
  invitationSecret: InvitationSecret | null;
  isLoading: boolean;
}

interface InvitationContextType extends InvitationState {
  invitationSecret: InvitationSecret | null;
  setInvitationSecret: (data: InvitationSecret) => void;
  clearInvitationSecret: () => void;
  hasInvitationData: () => boolean;
}

const initialState: InvitationState = {
  invitationSecret: null,
  isLoading: true,
};

const InvitationContext = createContext<InvitationContextType | undefined>(undefined);

interface InvitationProviderProps {
  children: ReactNode;
}

const STORAGE_KEY = "storeseo_invitation_data";

export function InvitationProvider({ children }: InvitationProviderProps) {
  const [state, setState] = useState<InvitationState>(initialState);

  // Load invitation data from sessionStorage on mount
  useEffect(() => {
    try {
      const storedData = sessionStorage.getItem(STORAGE_KEY);
      if (storedData) {
        const parsedData = JSON.parse(storedData) as InvitationSecret;
        setState({
          invitationSecret: parsedData,
          isLoading: false,
        });
      } else {
        setState((prev) => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error("Failed to load invitation data from sessionStorage:", error);
      // Clear corrupted data
      sessionStorage.removeItem(STORAGE_KEY);
      setState((prev) => ({ ...prev, isLoading: false }));
    }
  }, []);

  const setInvitationSecret = (data: InvitationSecret) => {
    try {
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify(data));
      setState({
        invitationSecret: data,
        isLoading: false,
      });
    } catch (error) {
      console.error("Failed to save invitation data to sessionStorage:", error);
      // Still update state even if storage fails
      setState({
        invitationSecret: data,
        isLoading: false,
      });
    }
  };

  const clearInvitationSecret = () => {
    try {
      sessionStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error("Failed to clear invitation data from sessionStorage:", error);
    }
    setState({
      invitationSecret: null,
      isLoading: false,
    });
  };

  const hasInvitationData = (): boolean => {
    return state.invitationSecret !== null;
  };

  const contextValue: InvitationContextType = {
    ...state,
    setInvitationSecret,
    clearInvitationSecret,
    hasInvitationData,
    // invitationSecret: state.invitationSecret,
  };

  return <InvitationContext.Provider value={contextValue}>{children}</InvitationContext.Provider>;
}

export function useInvitation(): InvitationContextType {
  const context = useContext(InvitationContext);
  if (!context) {
    throw new Error("useInvitation must be used within an InvitationProvider");
  }
  return context;
}
