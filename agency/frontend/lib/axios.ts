declare global {
  interface Window {
    ENV?: {
      PUBLIC_AGENCY_SERVER_URL?: string;
    };
  }
}

import axios, { AxiosError, type AxiosRequestConfig, type InternalAxiosRequestConfig } from "axios";

const API_BASE_URL =
  typeof window !== "undefined"
    ? window.ENV?.PUBLIC_AGENCY_SERVER_URL || "http://localhost:8000/v1"
    : process.env.PUBLIC_AGENCY_SERVER_URL || "http://localhost:8000/v1";

const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: { "Content-Type": "application/json" },
  withCredentials: true, // send cookies
});

let isRefreshing = false;
let refreshPromise: Promise<any> | null = null;

function refreshTokens() {
  // One refresh call shared between all pending requests
  if (!refreshPromise) {
    refreshPromise = axiosInstance.post("/auth/refresh-token").finally(() => {
      refreshPromise = null;
      isRefreshing = false;
    });
  }
  return refreshPromise;
}

// axiosInstance.interceptors.response.use(
//   (response) => response,
//   async (error: AxiosError) => {
//     const status = error.response?.status;
//     const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

//     // Determine if the failing request is a public auth endpoint where refresh should never be attempted
//     const url = originalRequest?.url || "";
//     const isRefreshEndpoint = url.includes("/auth/refresh-token");
//     const isPublicAuthEndpoint = [
//       "/auth/login",
//       "/auth/register",
//       "/auth/verify-email",
//       "/auth/resend-verification-email",
//       "/auth/create-workspace",
//       "/auth/logout",
//     ].some((p) => url.includes(p));

//     // Only attempt token refresh if:
//     // - We get a 401 status
//     // - This is not a retry attempt
//     // - The endpoint is not a public auth endpoint (login/register/etc.)
//     // - This is not the refresh-token endpoint itself (to avoid loops)
//     if (status === 401 && originalRequest && !originalRequest._retry && !isRefreshEndpoint && !isPublicAuthEndpoint) {
//       originalRequest._retry = true;

//       try {
//         if (!isRefreshing) {
//           isRefreshing = true;
//           await refreshTokens();
//         } else {
//           await refreshPromise; // wait for the ongoing refresh
//         }
//         return axiosInstance(originalRequest as AxiosRequestConfig); // retry original request
//       } catch (err) {
//         // If refresh fails, clear any auth state and let the error propagate
//         isRefreshing = false;
//         refreshPromise = null;
//         return Promise.reject(err);
//       }
//     }

//     return Promise.reject(error);
//   }
// );

export default axiosInstance;
