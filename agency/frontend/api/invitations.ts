import { toast } from "@storeware/polaris";
import { AxiosError } from "axios";
import { useAppMutation } from "storeware-tanstack-query";

import axios from "../lib/axios";
import type { ApiResponse, IInviteUserPayload } from "../types/auth";

export const useInviteUser = () => {
  return useAppMutation({
    mutationFn: async (payload: IInviteUserPayload) => {
      const response = await axios.post("/auth/invite-user", payload);
      return await response.data;
    },
    onSuccess: (data: ApiResponse<any>) => {
      toast.show(data.message || "Invitation sent");
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast.show(error.response?.data?.message);
      } else {
        toast.show("Failed to send invitation");
      }
    },
  });
};

export const useVerifyInvitation = () => {
  return useAppMutation({
    mutationFn: async (token: string) => {
      const response = await axios.get("/auth/verify-invitation", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return await response.data;
    },
    onSuccess: (data: ApiResponse<any>) => {
      // toast.show(data.message);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast.show(error.response?.data?.message);
      } else {
        toast.show("Invitation verification failed. Please try again.");
      }
    },
  });
};

export const useInviteUserRegister = () => {
  return useAppMutation({
    mutationFn: async (payload: {
      firstName: string;
      lastName?: string;
      password: string;
      email: string;
      secret: string;
    }) => {
      const { secret, ...userData } = payload;
      const response = await axios.post("/auth/invite-user-register", userData, {
        headers: {
          Authorization: `Bearer ${secret}`,
        },
      });
      return await response.data;
    },
    onSuccess: (data: ApiResponse<any>) => {
      toast.show(data.message);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast.show(error.response?.data?.message);
      } else {
        toast.show("Invitation registration failed. Please try again.");
      }
    },
  });
};

export const useJoinWorkspace = () => {
  return useAppMutation({
    mutationFn: async (payload: { email: string; secret: string }) => {
      const { secret, email } = payload;
      const response = await axios.post(
        "/auth/join-workspace",
        { email },
        {
          headers: {
            Authorization: `Bearer ${secret}`,
          },
        }
      );
      return await response.data;
    },
    onSuccess: (data: ApiResponse<any>) => {
      toast.show(data.message);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast.show(error.response?.data?.message);
      } else {
        toast.show("Failed to join workspace");
      }
    },
  });
};
