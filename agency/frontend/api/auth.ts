import { toast } from "@storeware/polaris";
import { AxiosError } from "axios";
import { useAppMutation } from "storeware-tanstack-query";
import type { ILoginUserResponse, IRegisterUserResponse, IWorkspaceSetupResult } from "~/server/types/auth";
import axios from "../lib/axios";
import type { ApiResponse } from "../types/auth";

export const useRegisterUser = () => {
  return useAppMutation({
    mutationFn: async (payload) => {
      const response = await axios.post("/auth/register", payload);
      return await response.data;
    },
    onSuccess: (data: ApiResponse<IRegisterUserResponse>) => {
      toast.show(data.message);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast.show(error.response?.data?.message);
      } else {
        toast.show("User registration failed");
      }
    },
  });
};

export const useLoginUser = () => {
  return useAppMutation({
    mutationFn: async (payload) => {
      const response = await axios.post("/auth/login", payload);
      return await response.data;
    },
    onSuccess: (data: ApiResponse<ILoginUserResponse>) => {
      toast.show(data.message);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast.show(error.response?.data?.message);
      } else {
        toast.show("Login failed. Please try again.");
      }
    },
  });
};

export const useVerifyEmail = () => {
  return useAppMutation({
    mutationFn: async (token: string) => {
      const response = await axios.post(`/auth/verify-email?secret=${encodeURIComponent(token)}`);
      return await response.data;
    },
    onSuccess: (data: ApiResponse<ILoginUserResponse>) => {
      console.log("data", data);
      toast.show(data.message);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast.show(error.response?.data?.message);
      } else {
        toast.show("Email verification failed. Please try again.");
      }
    },
  });
};

export const useCreateWorkspace = () => {
  return useAppMutation({
    mutationFn: async (payload: any) => {
      const response = await axios.post("/auth/create-workspace", payload);
      return await response.data;
    },
    onSuccess: (data: ApiResponse<IWorkspaceSetupResult>) => {
      toast.show(data.message);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast.show(error.response?.data?.message);
      } else {
        toast.show("Workspace creation failed");
      }
    },
  });
};

export const useLogout = () => {
  return useAppMutation({
    mutationFn: async () => {
      const response = await axios.post("/auth/logout");
      return await response.data;
    },
    onSuccess: (data: ApiResponse<{}>) => {
      toast.show(data.message);
    },
    onError: (error: unknown) => {
      if (error instanceof AxiosError) {
        toast.show(error.response?.data?.message);
      } else {
        toast.show("Logout failed");
      }
    },
  });
};

export const useGetUserProfile = () => {
  return useAppMutation({
    mutationFn: async () => {
      const response = await axios.get("/auth/me");
      return await response.data;
    },
    // onError: (error: unknown) => {
    //   if (error instanceof AxiosError) {
    //     toast.show(error.response?.data?.message);
    //   } else {
    //     toast.show("Failed to fetch user profile");
    //   }
    // },
  });
};
